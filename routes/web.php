<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Test route to verify AJAX functionality
Route::post('/test-ajax-profile', function (Request $request) {
    // Log the request for debugging
    \Log::info('Test AJAX Profile Request', [
        'headers' => $request->headers->all(),
        'data' => $request->all(),
        'expects_json' => $request->expectsJson(),
        'wants_json' => $request->wantsJson(),
        'ajax' => $request->ajax(),
    ]);

    // Check if it's an AJAX request
    if ($request->expectsJson() || $request->ajax()) {
        return response()->json([
            'error' => false,
            'message' => 'AJAX test successful!',
            'data' => [
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'received_headers' => [
                    'X-Requested-With' => $request->header('X-Requested-With'),
                    'Accept' => $request->header('Accept'),
                    'Content-Type' => $request->header('Content-Type'),
                ]
            ]
        ]);
    }

    // If not AJAX, redirect back
    return redirect()->back()->with('success', 'Form submitted successfully (non-AJAX)');
})->name('test.ajax.profile');
