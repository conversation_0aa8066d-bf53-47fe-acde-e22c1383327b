<!-- <PERSON><PERSON>ng nhập -->
<div class="modal fade" id="signin" tabindex="-1" aria-labelledby="signin" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content content-box">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="position: absolute; right: 1rem; top: 1rem; z-index: 1055; pointer-events: auto;"></button>
            <div class="modal-body content-upper">
                <h4 class="modal-title text-center">Rất vui lại được đón bạn</h4>

                <!-- Display validation errors for login -->
                <div id="login-errors" class="alert alert-danger mt-3" style="display: none;">
                    <ul class="mb-0" id="login-error-list"></ul>
                </div>

                <!-- Display success/warning messages -->
                @if (session('auth_warning_message'))
                    <div class="alert alert-warning mt-3">
                        {{ session('auth_warning_message') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('public.member.login.post') }}" id="login-form">
                    @csrf
                    <div class="form-group mt-4">
                        <input type="email" class="form-control"
                               name="email" id="login_email"
                               value="{{ old('email') }}"
                               placeholder="Username hoặc Email" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="password" class="form-control"
                               name="password" id="login_password"
                               placeholder="Mật khẩu" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-check mt-3">
                        <input type="checkbox" class="form-check-input" name="remember" id="remember" value="1">
                        <label class="form-check-label" for="remember">
                            Ghi nhớ đăng nhập
                        </label>
                    </div>
                    <div class="text-center my-4">
                        <a href="{{ route('public.member.password.request') }}">Quên mật khẩu ư?</a>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="display: none;"></span>
                        Đăng nhập
                    </button>
                </form>
                <div class="mt-4 text-center">
                    Bạn chưa có tài khoản?
                    <a href="#" data-bs-toggle="modal" data-bs-target="#signup" data-bs-dismiss="modal">Đăng ký ngay!</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Đăng ký -->
<div class="modal fade" id="signup" tabindex="-1" aria-labelledby="signup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content content-box">
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="position: absolute; right: 1rem; top: 1rem; z-index: 1055; pointer-events: auto;"></button>
            <div class="modal-body content-upper">
                <h4 class="modal-title text-center">Thế giới sôi động đang đón chờ...</h4>

                <!-- Display validation errors for registration -->
                <div id="register-errors" class="alert alert-danger mt-3" style="display: none;">
                    <ul class="mb-0" id="register-error-list"></ul>
                </div>

                <form method="POST" action="{{ route('public.member.register.post') }}" id="register-form">
                    @csrf
                    <div class="form-group mt-4">
                        <input type="text" class="form-control"
                               name="first_name" id="first_name"
                               value="{{ old('first_name') }}"
                               placeholder="Tên" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="text" class="form-control"
                               name="last_name" id="last_name"
                               value="{{ old('last_name') }}"
                               placeholder="Họ" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="email" class="form-control"
                               name="email" id="register_email"
                               value="{{ old('email') }}"
                               placeholder="Email" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="password" class="form-control"
                               name="password" id="register_password"
                               placeholder="Mật khẩu" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group mt-3">
                        <input type="password" class="form-control"
                               name="password_confirmation" id="password_confirmation"
                               placeholder="Lặp lại mật khẩu" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    @if (setting('member_enable_terms_and_policy_checkbox', false))
                        <div class="form-check mt-3">
                            <input type="checkbox" class="form-check-input" name="agree_terms_and_policy" id="agree_terms_and_policy" value="1" required>
                            <label class="form-check-label" for="agree_terms_and_policy">
                                Tôi đồng ý với <a href="#" target="_blank">điều khoản và chính sách</a>
                            </label>
                        </div>
                    @endif
                    <button type="submit" class="btn btn-primary w-100 mt-4">
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="display: none;"></span>
                        Đăng ký
                    </button>
                </form>
                <div class="mt-4 text-center">
                    Bạn đã có tài khoản?
                    <a href="#" data-bs-toggle="modal" data-bs-target="#signin" data-bs-dismiss="modal">Đăng nhập ở đây nhé</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle login form submission
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const spinner = submitBtn.querySelector('.spinner-border');
            const errorContainer = document.getElementById('login-errors');
            const errorList = document.getElementById('login-error-list');

            // Show loading state
            submitBtn.disabled = true;
            spinner.style.display = 'inline-block';
            errorContainer.style.display = 'none';

            // Clear previous errors
            this.querySelectorAll('.form-control').forEach(input => {
                input.classList.remove('is-invalid');
                input.nextElementSibling.textContent = '';
            });

            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log(data)
                if (data.error !== false) {
                    // Handle validation errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = this.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.classList.add('is-invalid');
                                input.nextElementSibling.textContent = data.errors[field][0];
                            }
                        });
                    } else {
                        // Show general error
                        errorList.innerHTML = `<li>${data.message || 'Đăng nhập thất bại'}</li>`;
                        errorContainer.style.display = 'block';
                    }
                } else {
                    // Success - redirect or close modal
                    if (data.data && data.data.next_url) {
                        window.location.href = data.data.next_url;
                    } else {
                        window.location.reload();
                    }
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                errorList.innerHTML = '<li>Có lỗi xảy ra, vui lòng thử lại</li>';
                errorContainer.style.display = 'block';
            })
            .finally(() => {
                // Hide loading state
                submitBtn.disabled = false;
                spinner.style.display = 'none';
            });
        });
    }

    // Handle register form submission
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const spinner = submitBtn.querySelector('.spinner-border');
            const errorContainer = document.getElementById('register-errors');
            const errorList = document.getElementById('register-error-list');

            // Show loading state
            submitBtn.disabled = true;
            spinner.style.display = 'inline-block';
            errorContainer.style.display = 'none';

            // Clear previous errors
            this.querySelectorAll('.form-control').forEach(input => {
                input.classList.remove('is-invalid');
                input.nextElementSibling.textContent = '';
            });

            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error !== false) {
                    // Handle validation errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = this.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.classList.add('is-invalid');
                                input.nextElementSibling.textContent = data.errors[field][0];
                            }
                        });
                    } else {
                        // Show general error
                        errorList.innerHTML = `<li>${data.message || 'Đăng ký thất bại'}</li>`;
                        errorContainer.style.display = 'block';
                    }
                } else {
                    if (data.data && data.data.next_url) {
                        window.location.href = data.data.next_url;
                    } else {
                        // Close modal and maybe show login modal
                        const signupModal = bootstrap.Modal.getInstance(document.getElementById('signup'));
                        if (signupModal) {
                            signupModal.hide();
                        }
                        // Show success message
                        Swal.fire({
                            icon: "success",
                            title: "Đăng ký thành công!",
                            text: "",
                            confirmButtonText: "Đóng",
                            confirmButtonColor: "var(--primary-color, #ff2b4a)"
                        }).then((result) => {
                            window.location.reload();
                        })
                    }
                }
            })
            .catch(error => {
                console.error('Register error:', error);
                errorList.innerHTML = '<li>Có lỗi xảy ra, vui lòng thử lại</li>';
                errorContainer.style.display = 'block';
            })
            .finally(() => {
                // Hide loading state
                submitBtn.disabled = false;
                spinner.style.display = 'none';
            });
        });
    }

    // Ensure modal close buttons work
    const modalCloseButtons = document.querySelectorAll('.modal .btn-close');
    modalCloseButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const modal = button.closest('.modal');
            if (modal) {
                // Try Bootstrap's modal hide method first
                if (window.bootstrap && bootstrap.Modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    } else {
                        // Create new modal instance and hide
                        const newModal = new bootstrap.Modal(modal);
                        newModal.hide();
                    }
                } else {
                    // Fallback: manually hide modal
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                    // Remove backdrop if exists
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }
            }
        });
    });
});
</script>
