// Wishlist functionality for place detail page
console.log("Wishlist script loading...");

document.addEventListener("click", function(e) {
    console.log("Click on:", e.target.className, e.target.tagName);

    // Check if clicked element or its parent is wishlist button
    const wishBtn = e.target.closest(".wishBtn");
    if (wishBtn) {
        console.log("Wishlist button clicked!", wishBtn);
        e.preventDefault();

        const placeId = wishBtn.dataset.placeId;
        const isAuthenticated = wishBtn.dataset.authenticated === "true";

        console.log("Place ID:", placeId);
        console.log("Is authenticated:", isAuthenticated);

        if (!isAuthenticated) {
            // Redirect to login or show auth modal
            const authModal = document.querySelector(
                '[data-bs-toggle="modal"][data-bs-target="#signup"]',
            );
            if (authModal) {
                authModal.click();
            }
            return;
        }

        // Make API call
        const csrfToken = document.querySelector("meta[name=csrf-token]")?.content;
        console.log("CSRF token:", csrfToken);

        fetch("/api/booking/wishlist/" + placeId, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": csrfToken,
                "Accept": "application/json"
            }
        })
        .then(response => {
            console.log("API response:", response);
            return response.json();
        })
        .then(data => {
            const icon = wishBtn.querySelector("i");
            const span = wishBtn.querySelector("span");

            if (data.is_wishlisted) {
                icon.className = "fa-solid fa-star";
                if (span) span.textContent = "Đã yêu thích";
                wishBtn.classList.add("active");
            } else {
                icon.className = "fa-thin fa-star";
                if (span) span.textContent = "Yêu thích";
                wishBtn.classList.remove("active");
            }
        })
        .catch(error => {
            console.error("API error:", error);
            Swal.fire({
                icon: "error",
                title: "Có lỗi xảy ra!",
                text: "Vui lòng thử lại sau.",
                confirmButtonText: "Đóng",
                confirmButtonColor: "var(--primary-color, #ff2b4a)"
            });
        });
    }
});

console.log("Wishlist script loaded!");
