{{-- Profile Page Template --}}
@php
    Theme::layout('default');
    Theme::set('pageTitle', '<PERSON><PERSON> sơ cá nhân');
@endphp

<div class="wishlist-bg" style="background-image: url({{ Theme::asset()->url('images/profile_bg.jpg') }});">
</div>

<main class="profile-page">
    @include(Theme::getThemeNamespace('partials.components.search-form'), [
        'isMini' => true,
        'demo' => false
    ])

    <div class="container sm">
        {{-- Profile Header --}}
        <section>
            <div class="profile">
                <img src="{{ auth('member')->user()->avatar_url }}"
                    alt="{{ auth('member')->user()->name }}">
                <h3>{{ auth('member')->user()->name }}</h3>
            </div>
        </section>

        {{-- Personal Information Form --}}
        <div class="persional-form">
            <h3>Thông tin cá nhân</h3>
            <form id="profile-form" method="POST" action="{{ route('public.member.post.settings') }}">
                @csrf

                {{-- Name Field --}}
                <label>Họ tên</label>
                <div class="group">
                    <input type="text" name="name" value="{{ auth('member')->user()->name }}"
                           class="form-control" required>
                    <button type="submit" class="btn btn-save">Lưu</button>
                </div>

                {{-- Hidden fields for API --}}
                @php
                    $user = auth('member')->user();
                    $fullName = $user->name;
                    $nameParts = explode(' ', trim($fullName));
                    $firstName = '';
                    $lastName = '';

                    if (count($nameParts) > 1) {
                        $lastName = array_pop($nameParts);
                        $firstName = implode(' ', $nameParts);
                    } else {
                        $firstName = $fullName;
                    }
                @endphp
                <input type="hidden" name="first_name" value="{{ $firstName }}">
                <input type="hidden" name="last_name" value="{{ $lastName }}">

                {{-- Email Field --}}
                <label>Địa chỉ Email</label>
                <input type="email" name="email" value="{{ auth('member')->user()->email }}"
                       class="form-control" readonly>

                {{-- Display validation errors --}}
                @if ($errors->any())
                    <div class="alert alert-danger mt-3">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                {{-- Display success message --}}
                @if (session('success'))
                    <div class="alert alert-success mt-3">
                        {{ session('success') }}
                    </div>
                @endif
            </form>

            {{-- Action Buttons --}}
            <div class="buttons">
                <button type="button" class="btn btn-delete" onclick="confirmDeleteAccount()">
                    Xóa tài khoản
                </button>
                <form method="POST" action="{{ route('public.member.logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-logout">
                        <i class="fa-light fa-person-to-door"></i> Đăng xuất
                    </button>
                </form>
            </div>
        </div>
    </div>
</main>

{{-- Include mobile menu --}}
{!! Theme::partial('components.mobile-menu') !!}

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize hidden fields on page load
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        const nameField = profileForm.querySelector('input[name="name"]');
        const firstNameField = profileForm.querySelector('input[name="first_name"]');
        const lastNameField = profileForm.querySelector('input[name="last_name"]');

        // Function to update hidden fields
        function updateHiddenFields() {
            const nameValue = nameField.value.trim();
            const { firstName, lastName } = splitName(nameValue);
            firstNameField.value = firstName;
            lastNameField.value = lastName;
        }

        // Update hidden fields whenever the name field changes
        nameField.addEventListener('input', updateHiddenFields);
        nameField.addEventListener('blur', updateHiddenFields);

        // Initial update
        updateHiddenFields();
    }

    // Handle profile form submission via AJAX
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get the name field value
            const nameField = this.querySelector('input[name="name"]');
            const nameValue = nameField.value.trim();

            // Split name into first_name and last_name
            const { firstName, lastName } = splitName(nameValue);

            // Update hidden fields
            this.querySelector('input[name="first_name"]').value = firstName;
            this.querySelector('input[name="last_name"]').value = lastName;

            const formData = new FormData(this);

            // Remove the 'name' field since backend expects first_name and last_name
            formData.delete('name');

            const submitBtn = this.querySelector('.btn-save');
            const originalText = submitBtn.textContent;

            submitBtn.textContent = 'Đang lưu...';
            submitBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error === false) {
                    submitBtn.textContent = 'Đã lưu';
                    submitBtn.style.background = '#28a745';

                    // Show success message
                    if (data.message) {
                        showNotification(data.message, 'success');
                    }

                    setTimeout(() => {
                        submitBtn.textContent = originalText;
                        submitBtn.style.background = '';
                        submitBtn.disabled = false;
                    }, 2000);
                } else {
                    throw new Error(data.message || 'Có lỗi xảy ra');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                submitBtn.textContent = 'Lỗi';
                submitBtn.style.background = '#dc3545';

                // Show error message
                showNotification(error.message || 'Có lỗi xảy ra khi cập nhật thông tin', 'error');

                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.background = '';
                    submitBtn.disabled = false;
                }, 2000);
            });
        });
    }
});

/**
 * Split a full name into first name and last name
 * Strategy: Everything before the last space becomes first_name,
 * everything after the last space becomes last_name
 */
function splitName(fullName) {
    if (!fullName || typeof fullName !== 'string') {
        return { firstName: '', lastName: '' };
    }

    const trimmedName = fullName.trim();

    if (!trimmedName) {
        return { firstName: '', lastName: '' };
    }

    // Find the last space in the name
    const lastSpaceIndex = trimmedName.lastIndexOf(' ');

    if (lastSpaceIndex === -1) {
        // No space found, put everything in first_name
        return { firstName: trimmedName, lastName: '' };
    }

    // Split at the last space
    const firstName = trimmedName.substring(0, lastSpaceIndex).trim();
    const lastName = trimmedName.substring(lastSpaceIndex + 1).trim();

    return { firstName, lastName };
}

// Function to show notifications
function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} notification-popup`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        animation: slideIn 0.3s ease-out;
    `;
    notification.textContent = message;

    // Add CSS animation
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function confirmDeleteAccount() {
    if (confirm('Bạn có chắc chắn muốn xóa tài khoản này? Thao tác này không thể hoàn tác.')) {
        // Add delete account logic here
        Swal.fire({
            icon: "error",
            title: "Có lỗi xảy ra!",
            text: "Không thể mở cửa sổ cài đặt vị trí. Vui lòng tải lại trang.",
            confirmButtonText: "Đóng",
            confirmButtonColor: "var(--primary-color, #ff2b4a)"
        });
    }
}
</script>
@endpush
