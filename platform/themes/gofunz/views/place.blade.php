@php Theme::set('section-name', __('Home')) @endphp

<main>
    @include(Theme::getThemeNamespace('partials.components.search-form'), [
        'isMini' => true,
    ])

    <div class="container md">
        <section class="mainWrap photo-gallery">
            <a href="{{route('public.map')}}" class="btn subBtn backBtn"><i class="fa-solid fa-arrow-left"></i></a>
            @php
                $isWishlisted = false;
                if (auth('member')->check() && isset($place)) {
                    // Check if place is in user's wishlist
                    $isWishlisted = auth('member')->user()->wishlistPlaces->contains($place->id);
                }
            @endphp
            <button class="btn added subBtn wishBtn {{ $isWishlisted ? 'active' : '' }}"
                data-place-id="{{ isset($place) ? $place->id : '' }}"
                data-authenticated="{{ auth('member')->check() ? 'true' : 'false' }}"
                title="{{ $isWishlisted ? 'Bỏ yêu thích' : 'Yêu thích' }}">
                <i class="fa-{{ $isWishlisted ? 'solid' : 'thin' }} fa-star"></i>
                <span>{{ $isWishlisted ? 'Đã yêu thích' : 'Yêu thích' }}</span>
            </button>
            @php
                $images = isset($place) ? $place->thumbnail : [
                    RvMedia::getImageUrl('demo/3.jpg'),
                    RvMedia::getImageUrl('demo/7.jpg'),
                    RvMedia::getImageUrl('demo/8.jpg'),
                    RvMedia::getImageUrl('demo/9.jpg'),
                ];
                // remove null from array
                $images = array_filter($images, function ($image) {
                    return !is_null($image);
                });
                if (count($images) >= 3) {
                    $slideTwoImages = array_slice($images, 0, 2);
                    // Remove 2 images from end of array
                    $images = array_slice($images, 2);
                } else {
                    $slideTwoImages = [];
                }
            @endphp
            <div class="mainContent">
                @include(Theme::getThemeNamespace('partials.components.place-swiper'), [
                    'images' => $images,
                    'demo' => !isset($place),
                ])
            </div>
            <aside class="sub-photos">
                @foreach ($slideTwoImages as $image)
                    <div class="sub-photo">
                        <img src="{{ Storage::disk('public')->url($image) }}" alt="">
                    </div>
                @endforeach
            </aside>
        </section>
        <div class="mainWrap detail-info">
            <div class="mainContent infors">
                <div class="s-head">
                    @if (isset($place))
                        <span> {{ $place['address'] }}, {{ $place['city'] }}, {{ $place['state'] }}</span>
                    @endif

                    <h1>{{ isset($place['name']) ? $place['name'] : '' }}</h1>
                </div>
                <div class="featured">
                    <div class="feature-inner">
                    @if (isset($place->services))
                        @foreach ($place->services as $service)
                            <span><i class="fa-light fa-{{ $service['icon'] }}"></i>{{ $service['name'] }}</span>
                        @endforeach
                    @endif
                    </div>
                </div>
                <section class="description">
                    <!-- Show description html content -->
                    @if (isset($place['description']))
                        {!! $place['description'] !!}
                    @else
                        <p>Không có mô tả cho địa điểm này.</p>
                    @endif
                </section>
                @include(Theme::getThemeNamespace('partials.components.place-posts'), [
                    'posts' => $place->posts,
                ])
                <section>
                    <div class="s-title">
                        <span class="location-title-clickable" onclick="openLocationModal()" style="cursor: pointer; display: inline-flex; align-items: center; transition: color 0.3s ease;" onmouseover="this.style.color='#007bff'" onmouseout="this.style.color=''">
                            <i class="fa-light fa-location-dot"></i>Khám phá địa điểm
                        </span>
                        <span class="distance" id="distance-display">
                            <span class="distance-loading" style="display: none; color: #666; font-size: 0.9em;">
                                <i class="fa-solid fa-spinner fa-spin"></i> Đang tính khoảng cách...
                            </span>
                            <span class="distance-result" style="display: none; color: #28a745; font-weight: 500; font-size: 0.9em;"></span>
                            <span class="distance-error" style="display: none; color: #dc3545; font-size: 0.9em; cursor: help;" onclick="openLocationModal()" title="Nhấp để cài đặt lại vị trí">
                                <i class="fa-light fa-location-slash"></i> Không thể tính khoảng cách
                            </span>
                        </span>
                    </div>

                    <script>
                    function openLocationModal() {
                        console.log('Opening location modal...');

                        // Check if the modal function exists and call it
                        if (typeof window.showLocationModal === 'function') {
                            window.showLocationModal();
                        } else {
                            // Fallback: try to show modal directly
                            const locationModal = document.getElementById('locationModal');
                            if (locationModal) {
                                if (window.bootstrap && bootstrap.Modal) {
                                    const bsModal = new bootstrap.Modal(locationModal);
                                    bsModal.show();
                                } else {
                                    // Manual show as last resort
                                    locationModal.classList.add('show');
                                    locationModal.style.display = 'block';
                                    document.body.classList.add('modal-open');

                                    // Add backdrop
                                    const backdrop = document.createElement('div');
                                    backdrop.className = 'modal-backdrop fade show';
                                    document.body.appendChild(backdrop);
                                }
                            } else {
                                console.error('Location modal not found');
                                Swal.fire({
                                    icon: "error",
                                    title: "Có lỗi xảy ra!",
                                    text: "Không thể mở cửa sổ cài đặt vị trí. Vui lòng tải lại trang.",
                                    confirmButtonText: "Đóng",
                                    confirmButtonColor: "var(--primary-color, #ff2b4a)"
                                });
                            }
                        }
                    }
                    </script>
                    <div class="map-container" style="min-height: 300px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <style>
                            .map-container iframe {
                                width: 100%;
                                height: 300px;
                                border: none;
                                border-radius: 8px;
                            }
                            .map-placeholder {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                height: 300px;
                                background-color: #f8f9fa;
                                border: 2px dashed #dee2e6;
                                border-radius: 8px;
                                color: #6c757d;
                                text-align: center;
                            }
                            .map-placeholder i {
                                margin-right: 8px;
                                font-size: 1.2em;
                            }
                            .distance-loading {
                                color: #666 !important;
                                font-size: 0.9em !important;
                            }
                            .distance-result {
                                color: #28a745 !important;
                                font-weight: 500 !important;
                            }
                            .distance-error {
                                color: #dc3545 !important;
                                font-size: 0.9em !important;
                                cursor: help !important;
                            }

                            /* Booking form styles */
                            .reservation-form .form-group {
                                margin-bottom: 15px;
                            }
                            .reservation-form .error-message {
                                color: #dc3545;
                                font-size: 0.875em;
                                margin-top: 5px;
                                display: none;
                            }
                            .reservation-form .error-message.show {
                                display: block;
                            }
                            .reservation-form input.error,
                            .reservation-form textarea.error {
                                border-color: #dc3545;
                                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
                            }
                            .reservation-form button[type="submit"] {
                                position: relative;
                                overflow: hidden;
                            }
                            .reservation-form button[type="submit"]:disabled {
                                opacity: 0.6;
                                cursor: not-allowed;
                            }
                            .btn-loading {
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                white-space: nowrap;
                            }
                        </style>
                        @if(isset($place) && !empty($place->iframe))
                            @php
                                $iframeContent = trim($place->iframe);

                                // Check if it's a complete iframe tag or just a URL
                                if (strpos($iframeContent, '<iframe') !== false) {
                                    // It's already a complete iframe tag - use as is
                                    $processedIframe = $iframeContent;
                                } else {
                                    // It's just a URL, wrap it in iframe tag with proper attributes
                                    // Clean the URL and ensure it's properly escaped
                                    $cleanUrl = filter_var($iframeContent, FILTER_SANITIZE_URL);

                                    // Validate that it's a valid URL and from trusted domains
                                    if (filter_var($cleanUrl, FILTER_VALIDATE_URL) &&
                                        (strpos($cleanUrl, 'google.com/maps') !== false ||
                                         strpos($cleanUrl, 'maps.google.com') !== false
                                        )) {

                                        $processedIframe = '<iframe src="' . e($cleanUrl) . '" width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>';
                                    } else {
                                        // Invalid or untrusted URL
                                        $processedIframe = '<div class="map-placeholder"><p><i class="fa-light fa-exclamation-triangle"></i> URL không hợp lệ hoặc không được hỗ trợ.</p></div>';
                                    }
                                }
                            @endphp
                            {!! $processedIframe !!}
                        @endif
                    </div>
                </section>
            </div>
            @include(Theme::getThemeNamespace('partials.components.booking-form'), [
                'place' => $place,
            ])
        </div>
    </div>
</main>
@php
    Theme::asset()->container('footer')->remove('scroll-handler');

    Theme::asset()
        ->container('footer')
        ->writeScript(
            'map-page',
            'window.addEventListener("scroll", function () {

                    const header = document.querySelector("header");
                    const triggerY = 50; // hoặc dùng getBoundingClientRect() để linh hoạt hơn

                if (window.scrollY > triggerY) {
                    header.classList.add("sticky-bg");
                    header.classList.add("map-page");
                } else {
                    header.classList.remove("sticky-bg");
                    header.classList.remove("map-page");
                }
            });',
            ['jquery'],
        );
    Theme::asset()
        ->container('footer')
        ->writeScript(
            'place-swiper',
            'document.addEventListener("DOMContentLoaded", function() {
                const swiper = new Swiper(".main-photo", {
                    slidesPerView: 1,
                    spaceBetween: 10,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                    },
                });
            });
            var swiper = new Swiper(".Xevents", {
                slidesPerView: "auto",
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                loop: true,
            });
            ',
            ['swiper'],
        );

    Theme::asset()->container('footer')->add('geolocation-utils', Theme::asset()->url('js/geolocation-utils.js'), ['jquery']);

    // Add distance calculation script using Google Maps Distance Matrix API
    $placeCoordinates = isset($place) && !empty($place->coordinates) ? $place->coordinates : null;
    if ($placeCoordinates) {
        $coords = explode(',', $placeCoordinates);
        $placeLat = trim($coords[0] ?? '');
        $placeLng = trim($coords[1] ?? '');



        Theme::asset()
            ->container('footer')
            ->writeScript(
                'distance-calculation',
                'document.addEventListener("DOMContentLoaded", function() {
                    const placeCoordinates = {
                        latitude: ' . $placeLat . ',
                        longitude: ' . $placeLng . '
                    };

                    function calculateAndDisplayDistance() {
                        const distanceDisplay = document.getElementById("distance-display");
                        const loadingElement = distanceDisplay.querySelector(".distance-loading");
                        const resultElement = distanceDisplay.querySelector(".distance-result");
                        const errorElement = distanceDisplay.querySelector(".distance-error");

                        // Show loading state
                        loadingElement.style.display = "inline";
                        resultElement.style.display = "none";
                        errorElement.style.display = "none";

                        // Use the global geolocation wrapper with cookie caching
                        GeolocationUtils.getCurrentPosition(
                            function(position) {
                                const userLat = position.coords.latitude;
                                const userLng = position.coords.longitude;

                                // Log if using cached location
                                if (position.fromCache) {
                                    console.log("Using cached user location for distance calculation");
                                }

                                // Use Google Maps Distance Matrix API
                                calculateDistanceWithGoogleMaps(userLat, userLng, placeCoordinates.latitude, placeCoordinates.longitude);
                            },
                            function(error) {
                                let errorMessage;
                                switch(error.code) {
                                    case error.PERMISSION_DENIED:
                                        errorMessage = "Bạn đã từ chối chia sẻ vị trí";
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        errorMessage = "Không thể xác định vị trí";
                                        break;
                                    case error.TIMEOUT:
                                        errorMessage = "Hết thời gian chờ định vị";
                                        break;
                                    default:
                                        errorMessage = "Lỗi không xác định";
                                        break;
                                }
                                showDistanceError(errorMessage);
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 300000, // 5 minutes
                                useCache: true // Enable cookie caching
                            }
                        );
                    }

                    async function calculateDistanceWithGoogleMaps(userLat, userLng, placeLat, placeLng) {
                        try {
                            // Use the new Google Maps API loading method
                            if (typeof google === "undefined" || !google.maps) {
                                console.warn("Google Maps API not available, using fallback calculation");
                                fallbackDistanceCalculation(userLat, userLng, placeLat, placeLng);
                                return;
                            }

                            // Import the routes library which contains DistanceMatrixService
                            const { DistanceMatrixService } = await google.maps.importLibrary("routes");

                            const service = new DistanceMatrixService();

                            service.getDistanceMatrix({
                                origins: [`${userLat},${userLng}`],
                                destinations: [`${placeLat},${placeLng}`],
                                travelMode: google.maps.TravelMode.DRIVING,
                                unitSystem: google.maps.UnitSystem.METRIC,
                                avoidHighways: false,
                                avoidTolls: false
                            }, function(response, status) {
                                if (status === "OK" && response.rows && response.rows.length > 0) {
                                    const element = response.rows[0].elements[0];

                                    if (element.status === "OK") {
                                        const distance = element.distance;
                                        const duration = element.duration;

                                        // Convert distance to Vietnamese
                                        let distanceVietnamese = distance.text;
                                        distanceVietnamese = distanceVietnamese.replace(/km/g, "km");
                                        distanceVietnamese = distanceVietnamese.replace(/m/g, "m");

                                        // Convert duration to Vietnamese
                                        let durationVietnamese = "";
                                        if (duration && duration.text) {
                                            durationVietnamese = duration.text;
                                            durationVietnamese = durationVietnamese.replace(/mins?/g, "phút");
                                            durationVietnamese = durationVietnamese.replace(/min/g, "phút");
                                            durationVietnamese = durationVietnamese.replace(/hours?/g, "giờ");
                                            durationVietnamese = durationVietnamese.replace(/hour/g, "giờ");
                                            durationVietnamese = durationVietnamese.replace(/hrs?/g, "giờ");
                                            durationVietnamese = durationVietnamese.replace(/hr/g, "giờ");
                                            durationVietnamese = durationVietnamese.replace(/days?/g, "ngày");
                                            durationVietnamese = durationVietnamese.replace(/day/g, "ngày");
                                        }

                                        let distanceText = `(cách ${distanceVietnamese}`;
                                        if (durationVietnamese) {
                                            distanceText += ` - ${durationVietnamese}`;
                                        }
                                        distanceText += ")";

                                        showDistanceResult(distanceText);
                                    } else {
                                        console.warn("Distance Matrix element status:", element.status);
                                        // Fallback to simple calculation
                                        fallbackDistanceCalculation(userLat, userLng, placeLat, placeLng);
                                    }
                                } else {
                                    console.warn("Distance Matrix API status:", status);
                                    // Fallback to simple calculation
                                    fallbackDistanceCalculation(userLat, userLng, placeLat, placeLng);
                                }
                            });
                        } catch (error) {
                            console.error("Error with Google Maps Distance Matrix API:", error);
                            // Fallback to simple calculation
                            fallbackDistanceCalculation(userLat, userLng, placeLat, placeLng);
                        }
                    }

                    function fallbackDistanceCalculation(userLat, userLng, placeLat, placeLng) {
                        // Simple haversine formula for distance calculation as fallback
                        const R = 6371; // Earth radius in kilometers
                        const dLat = (placeLat - userLat) * Math.PI / 180;
                        const dLng = (placeLng - userLng) * Math.PI / 180;
                        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                                Math.cos(userLat * Math.PI / 180) * Math.cos(placeLat * Math.PI / 180) *
                                Math.sin(dLng/2) * Math.sin(dLng/2);
                        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                        const distanceInKm = R * c;

                        let distanceText;
                        if (distanceInKm < 1) {
                            distanceText = `(cách ${Math.round(distanceInKm * 1000)} mét)`;
                        } else {
                            distanceText = `(cách ${distanceInKm.toFixed(1)} km)`;
                        }

                        showDistanceResult(distanceText);
                    }

                    function showDistanceResult(text) {
                        const distanceDisplay = document.getElementById("distance-display");
                        const loadingElement = distanceDisplay.querySelector(".distance-loading");
                        const resultElement = distanceDisplay.querySelector(".distance-result");
                        const errorElement = distanceDisplay.querySelector(".distance-error");

                        loadingElement.style.display = "none";
                        errorElement.style.display = "none";
                        resultElement.textContent = text;
                        resultElement.style.display = "inline";
                    }

                    function showDistanceError(message) {
                        const distanceDisplay = document.getElementById("distance-display");
                        const loadingElement = distanceDisplay.querySelector(".distance-loading");
                        const resultElement = distanceDisplay.querySelector(".distance-result");
                        const errorElement = distanceDisplay.querySelector(".distance-error");

                        loadingElement.style.display = "none";
                        resultElement.style.display = "none";
                        errorElement.title = message;
                        errorElement.style.display = "inline";
                    }

                    // Start distance calculation with a small delay to ensure Google Maps API is loaded
                    setTimeout(function() {
                        calculateAndDisplayDistance();
                    }, 1000);
                });',
                ['google-maps-api', 'geolocation-utils'],
            );
    }

    // Add booking form JavaScript
    Theme::asset()
        ->container('footer')
        ->writeScript(
            'booking-form',
            'document.addEventListener("DOMContentLoaded", function() {
                const bookingForm = document.getElementById("booking-form");
                const submitBtn = document.getElementById("submit-booking-btn");
                const btnText = submitBtn.querySelector(".btn-text");
                const btnLoading = submitBtn.querySelector(".btn-loading");

                if (bookingForm) {
                    bookingForm.addEventListener("submit", function(e) {
                        e.preventDefault();

                        // Clear previous errors
                        clearErrors();

                        // Show loading state
                        setLoadingState(true);

                        // Get form data
                        const formData = new FormData(bookingForm);

                        // Submit via AJAX
                        fetch("' . route('public.booking.store') . '", {
                            method: "POST",
                            body: formData,
                            headers: {
                                "X-Requested-With": "XMLHttpRequest",
                                "Accept": "application/json"
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            setLoadingState(false);

                            if (data.success) {
                                // Show success message
                                Swal.fire({
                                    icon: "success",
                                    title: "Đặt bàn thành công!",
                                    html: `
                                        <div style="text-align: left; margin: 20px 0;">
                                            <p><strong>Địa điểm:</strong> ${data.data.place_name}</p>
                                            <p><strong>Thời gian:</strong> ${data.data.booking_time}</p>
                                            <p><strong>Số người:</strong> ${data.data.number_people}</p>
                                            <p><strong>Số điện thoại:</strong> ${data.data.phone_number}</p>
                                        </div>
                                        <p style="color: #28a745; font-weight: 500;">Chúng tôi sẽ liên hệ với bạn sớm nhất!</p>
                                    `,
                                    confirmButtonText: "Đóng",
                                    confirmButtonColor: "var(--primary-color, #ff2b4a)"
                                });

                                // Reset form
                                bookingForm.reset();
                            } else {
                                // Show error message
                                if (data.errors) {
                                    showErrors(data.errors);
                                } else {
                                    Swal.fire({
                                        icon: "error",
                                        title: "Có lỗi xảy ra!",
                                        text: data.message || "Vui lòng thử lại sau.",
                                        confirmButtonText: "Đóng",
                                        confirmButtonColor: "var(--primary-color, #ff2b4a)"
                                    });
                                }
                            }
                        })
                        .catch(error => {
                            setLoadingState(false);
                            console.error("Booking error:", error);

                            Swal.fire({
                                icon: "error",
                                title: "Có lỗi xảy ra!",
                                text: "Không thể kết nối đến máy chủ. Vui lòng thử lại sau.",
                                confirmButtonText: "Đóng",
                                confirmButtonColor: "var(--primary-color, #ff2b4a)"
                            });
                        });
                    });
                }

                function setLoadingState(loading) {
                    if (loading) {
                        submitBtn.disabled = true;
                        btnText.style.display = "none";
                        btnLoading.style.display = "inline";
                    } else {
                        submitBtn.disabled = false;
                        btnText.style.display = "inline";
                        btnLoading.style.display = "none";
                    }
                }

                function clearErrors() {
                    const errorMessages = document.querySelectorAll(".error-message");
                    const errorInputs = document.querySelectorAll(".error");

                    errorMessages.forEach(msg => {
                        msg.classList.remove("show");
                        msg.textContent = "";
                    });

                    errorInputs.forEach(input => {
                        input.classList.remove("error");
                    });
                }

                function showErrors(errors) {
                    Object.keys(errors).forEach(field => {
                        const errorElement = document.getElementById(field + "-error");
                        const inputElement = document.querySelector(`[name="${field}"]`);

                        if (errorElement && errors[field][0]) {
                            errorElement.textContent = errors[field][0];
                            errorElement.classList.add("show");
                        }

                        if (inputElement) {
                            inputElement.classList.add("error");
                        }
                    });
                }
            });',
            ['sweetalert2'],
        );

    // Add wishlist functionality using external script
    Theme::asset()->container('footer')->add('wishlist', Theme::asset()->url('js/wishlist.js'), ['jquery', 'sweetalert2']);
@endphp
