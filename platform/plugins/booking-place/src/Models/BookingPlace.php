<?php

namespace Bo<PERSON>ble\BookingPlace\Models;

use Bo<PERSON>ble\Base\Models\BaseModel;
use <PERSON><PERSON>ble\Blog\Models\Post;
use Bo<PERSON>ble\BookingPlace\Enums\BookingPlaceStatusEnum;
use Botble\Member\Models\Member;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class BookingPlace extends BaseModel
{
    use HasFactory;

    protected $table = 'booking_places';

    protected $fillable = [
        'name',
        'status',
        'slug',
        'description',
        'thumbnail',
        'reservation_start_time',
        'reservation_end_time',
        'address',
        'address_2',
        'city',
        'state',
        'postcode',
        'country',
        'coordinates',
        'phone_numbers',
        'emails',
        'social_links',
        'website',
        'iframe',
    ];

    protected $casts = [
        'thumbnail' => 'array',
        'phone_numbers' => 'array',
        'emails' => 'array',
        'social_links' => 'array',
        'status' => BookingPlaceStatusEnum::class,
    ];

    public function categories()
    {
        return $this->belongsToMany(BookingCategory::class, 'booking_places_categories', 'place_id', 'category_id');
    }

    public function services()
    {
        return $this->belongsToMany(BookingService::class, 'booking_services_places', 'place_id', 'service_id');
    }

    public function bookingOrders()
    {
        return $this->hasMany(BookingOrder::class, 'place_id');
    }

    protected static function newFactory()
    {
        return \Database\Factories\BookingPlace\BookingPlaceFactory::new();
    }

    public function posts()
    {
        return $this->belongsToMany(Post::class, 'booking_blog_places', 'place_id', 'post_id');
    }

    public function wishlists(): HasMany
    {
        return $this->hasMany(BookingPlaceWishlist::class, 'place_id');
    }

    public function wishlistedBy(): BelongsToMany
    {
        return $this->belongsToMany(
            Member::class,
            'booking_place_wishlists',
            'place_id',
            'member_id'
        )->withTimestamps();
    }

    public function isWishlistedBy(?int $memberId = null): bool
    {
        if (!$memberId) {
            return false;
        }

        return $this->wishlists()->where('member_id', $memberId)->exists();
    }

    /**
     * Scope to get only published places
     */
    public function scopeWherePublished($query)
    {
        return $query->where('status', BookingPlaceStatusEnum::PUBLISHED);
    }

    /**
     * Scope to filter places within a geographic bounding box and calculate distances from a reference point.
     *
     * This scope performs geospatial filtering on records with coordinate data stored as comma-separated
     * latitude,longitude strings. It filters records within a bounding box and calculates the distance
     * from each record to a reference point using MySQL's ST_Distance_Sphere function.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query The query builder instance
     * @param array $box Bounding box coordinates with keys: minLat, maxLat, minLng, maxLng
     * @param float $inputLat Reference point latitude (-90 to 90)
     * @param float $inputLng Reference point longitude (-180 to 180)
     * @param float $radiusKm Maximum distance from reference point in kilometers
     * @return \Illuminate\Database\Eloquent\Builder
     *
     * @example
     * $box = [
     *     'minLat' => 40.0,
     *     'maxLat' => 41.0,
     *     'minLng' => -74.0,
     *     'maxLng' => -73.0
     * ];
     * $places = BookingPlace::withinGeographicRadius($box, 40.7128, -74.0060, 25)->get();
     */
    public function scopeWithinGeographicRadius($query, array $box, float $inputLat, float $inputLng, float $radiusKm)
    {
        return $query
            ->whereNotNull('coordinates')
            ->where('coordinates', '!=', '')
            ->whereRaw("
                CAST(SUBSTRING_INDEX(coordinates, ',', 1) AS DECIMAL(10,8)) BETWEEN ? AND ?
                AND CAST(SUBSTRING_INDEX(coordinates, ',', -1) AS DECIMAL(11,8)) BETWEEN ? AND ?
            ", [$box['minLat'], $box['maxLat'], $box['minLng'], $box['maxLng']])
            ->selectRaw("booking_places.*, 
                ST_Distance_Sphere(
                    ST_GeomFromText(CONCAT('POINT(',
                        SUBSTRING_INDEX(coordinates, ',', -1), ' ',
                        SUBSTRING_INDEX(coordinates, ',', 1),
                    ')')),
                    ST_GeomFromText(CONCAT('POINT(', ?, ' ', ?, ')'))
                ) as distance_meters
            ", [$inputLng, $inputLat])
            ->havingRaw('distance_meters <= ?', [$radiusKm * 1000]); // Convert km to meters
    }

    public function scopeApplyQuerySearch($query, array $filters)
    {
        if (!empty($filters['location'])) {
            $query->where(function ($query) use ($filters) {
                $query->where('address', 'LIKE', '%' . $filters['location'] . '%');
                $query->orWhere('address_2', 'LIKE', '%' . $filters['location'] . '%');
                $query->orWhere('name', 'LIKE', '%' . $filters['location'] . '%');
            });
        }

        if (!empty($filters['datetime'])) {
            // Extract only the time portion from the input datetime
            $requestedTime = $this->parseTimeFromDateTime($filters['datetime']);

            if ($requestedTime) {
                // Find places that are available at the requested time
                // Handle both normal time ranges and overnight bookings (crossing midnight)
                $query->where(function ($query) use ($requestedTime) {
                    $query->whereNotNull('reservation_start_time')
                          ->whereNotNull('reservation_end_time')
                          ->where(function ($query) use ($requestedTime) {
                              // Case 1: Normal time range (start <= end, e.g., 09:00 - 17:00)
                              $query->where(function ($query) use ($requestedTime) {
                                  $query->whereRaw('TIME(reservation_start_time) <= TIME(reservation_end_time)')
                                        ->whereRaw('TIME(?) BETWEEN TIME(reservation_start_time) AND TIME(reservation_end_time)', [$requestedTime]);
                              })
                              // Case 2: Overnight booking (start > end, e.g., 17:00 - 01:00)
                              ->orWhere(function ($query) use ($requestedTime) {
                                  $query->whereRaw('TIME(reservation_start_time) > TIME(reservation_end_time)')
                                        ->where(function ($query) use ($requestedTime) {
                                            // Time is either >= start_time OR <= end_time
                                            $query->whereRaw('TIME(?) >= TIME(reservation_start_time)', [$requestedTime])
                                                  ->orWhereRaw('TIME(?) <= TIME(reservation_end_time)', [$requestedTime]);
                                        });
                              });
                          });
                });
            }
        }

        if (!empty($filters['category'])) {
            $categoryId = $filters['category'];

            $query->whereHas('categories', function ($categoryQuery) use ($categoryId) {
                $categoryQuery->where('booking_categories.id', $categoryId);
            });
        }

        return $query;
    }

    /**
     * Extract time portion from datetime filter for time-only comparison
     *
     * @param string $datetime Input datetime (e.g., "17/08/2025 22:00")
     * @return string|null Extracted time in H:i:s format (e.g., "22:00:00") or null if invalid
     */
    private function parseTimeFromDateTime($datetime)
    {
        try {
            // Handle URL decoding if needed
            $datetime = urldecode($datetime);

            // Parse the datetime from DD/MM/YYYY HH:MM format
            $parsedDate = \DateTime::createFromFormat('d/m/Y H:i', $datetime);

            if ($parsedDate === false) {
                // Try alternative formats
                $parsedDate = \DateTime::createFromFormat('d/m/Y G:i', $datetime); // Single digit hour
                if ($parsedDate === false) {
                    // Try parsing just the time portion if full datetime parsing fails
                    $parsedDate = \DateTime::createFromFormat('H:i', $datetime);
                    if ($parsedDate === false) {
                        $parsedDate = \DateTime::createFromFormat('G:i', $datetime); // Single digit hour
                        if ($parsedDate === false) {
                            return null;
                        }
                    }
                }
            }

            // Return only the time portion in H:i:s format
            return $parsedDate->format('H:i:s');

        } catch (\Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::warning('Failed to parse time from datetime filter: ' . $datetime, ['error' => $e->getMessage()]);
            return null;
        }
    }
}
