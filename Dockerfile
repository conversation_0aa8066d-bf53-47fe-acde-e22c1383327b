# Multi-stage Dockerfile for Botble CMS

# Stage 1: Node.js for asset building
FROM node:20-alpine AS assets

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY webpack.mix.js ./

# Copy platform directory for workspace packages
COPY . ./

# Install Node.js dependencies
RUN npm ci --production=false

# Build assets for all themes and plugins
RUN npm run production

# Stage 2: PHP production image
FROM php:8.3-fpm-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    bash \
    git \
    curl \
    libpng-dev \
    libzip-dev \
    zip \
    unzip \
    oniguruma-dev \
    freetype-dev \
    libjpeg-turbo-dev \
    libwebp-dev \
    supervisor \
    nginx

# Install PHP extensions
RUN docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp

RUN docker-php-ext-install \
    pdo_mysql \
    mbstring \
    zip \
    exif \
    pcntl \
    gd \
    bcmath

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create application directory
WORKDIR /var/www/html

# Copy application code first
COPY . .

# Install PHP dependencies
RUN composer install

# Generate Composer autoloader
RUN composer dump-autoload --optimize

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache \
    && chmod -R 755 /var/www/html/public

# Create necessary directories
RUN mkdir -p /var/www/html/storage/logs \
    && mkdir -p /var/www/html/storage/framework/cache \
    && mkdir -p /var/www/html/storage/framework/sessions \
    && mkdir -p /var/www/html/storage/framework/views \
    && chown -R www-data:www-data /var/www/html/storage

# Publish assets built in the Node.js stage
RUN php artisan cms:publish:assets

# Publish vendor assets
RUN php artisan vendor:publish --tag=laravel-assets --ansi --force

# Ensure storage directories and files are properly set up
RUN mkdir -p /var/www/html/public/storage \
    && if [ -d "/var/www/html/storage/app/public" ]; then \
        cp -r /var/www/html/storage/app/public/* /var/www/html/public/storage/ || true; \
    fi

# Copy configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisord.conf
COPY docker/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh

# Make entrypoint executable
RUN chmod +x /usr/local/bin/entrypoint.sh

# Create required directories for supervisor logs
RUN mkdir -p /var/log/supervisor

# Expose port
EXPOSE 80

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Default command
CMD ["start"]
